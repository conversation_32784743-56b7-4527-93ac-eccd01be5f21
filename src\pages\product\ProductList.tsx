"use client"; 
import { useState, useEffect } from 'react';
import Link from "next/link";
import { Package, Search, Filter, Plus, Edit, Trash2, ArrowUpDown } from 'lucide-react';


interface Produit {
  id_produit: string;
  nom: string;
  reference: string;  
  type_stock: string;
  prix: number;
  date_peremption: string;
  code_barre: number;
  marque: string;
  description: string;
  unite_mesure: string;
  prix_achat: number;
  prix_vente: number;
  TVA: number;
  created_at: string;
  updated_at: string;
  stock_set?: { quantite: number; location?: string; batchNumber?: string }[];
}

const getStatusBadge = (quantite: number) => {
  let status;
  if (quantite === 0) {
    status = 'out-of-stock';
  } else if (quantite <= 10) {
    status = 'low-stock';
  } else {
    status = 'in-stock';
  }

  switch (status) {
    case 'in-stock':
      return <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">En stock</span>;
    case 'low-stock':
      return <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Stock faible</span>;
    case 'out-of-stock':
      return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Rupture</span>;
    default:
      return null;
  }
};

// Fonction pour obtenir le label de la catégorie
const getCategorieLabel = (categorie: string) => {
  const categoriesMap: {[key: string]: string} = {
    'FINIS': 'Produit fini',
    'MATIERE': 'Matière première',
    'EMBALLAGE': 'Emballage',
    'SEMI_FINI': 'Produit semi-fini',
    'CONSOMMABLE': 'Consommable'
  };
  
  return categoriesMap[categorie] || categorie;
};

// Fonction pour calculer la quantité totale en stock
const getStockQuantite = (produit: Produit) => {
  if (!produit.stock_set || produit.stock_set.length === 0) {
    return 0;
  }
  return produit.stock_set.reduce((total, stock) => total + stock.quantite, 0);
};

const ProductsList: React.FC = () => {
  const [produits, setProduits] = useState<Produit[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Récupérer les produits depuis l'API
  useEffect(() => {
    const fetchProduits = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://localhost:8000/api/produits/getAllProducts/');
        if (!response.ok) {
          throw new Error('Erreur lors du chargement des produits');
        }
        const data = await response.json();
        setProduits(data);
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement des produits');
        setLoading(false);
        console.error('Erreur de chargement:', err);
      }
    };
    
    fetchProduits();
  }, []);
  
  // Extraire les catégories uniques
  const categories = Array.from(new Set(produits.map(produit => produit.type_stock)));
  
  // Filtrer les produits selon la recherche et la catégorie
  const filteredProduits = produits.filter(produit => {
    const matchesSearch = produit.nom.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          produit.reference.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === '' || produit.type_stock === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Fonction pour supprimer un produit
  const handleDelete = async (id: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce produit?')) {
      try {
        const response = await fetch(`http://localhost:8000/api/produit-detail/${id}/`, {
          method: 'DELETE'
        });
        
        if (!response.ok) {
          throw new Error('Erreur lors de la suppression du produit');
        }
        
        setProduits(produits.filter(produit => (produit.id_produit !== id)));
      } catch (err) {
        setError('Erreur lors de la suppression du produit');
        console.error('Erreur de suppression:', err);
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Produits</h1>
          <p className="mt-1 text-sm text-gray-500">
            Gérez votre catalogue de produits
          </p>
        </div>
        <Link
          href="/products/productForm"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="mr-2 h-4 w-4" />
          Ajouter un produit
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Rechercher un produit..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter className="h-5 w-5 text-gray-400" />
            </div>
            <select
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              <option value="">Toutes les catégories</option>
              {categories.map((category) => (
                <option key={category} value={category}>{getCategorieLabel(category)}</option>
              ))}
            </select>
          </div>
          
          <div className="flex justify-end">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <ArrowUpDown className="mr-2 h-4 w-4" />
              Trier par
            </button>
          </div>
        </div>
      </div>

      {/* État de chargement ou d'erreur */}
      {loading && (
        <div className="bg-white shadow rounded-lg p-4">
          <p className="text-center text-gray-500">Chargement des produits...</p>
        </div>
      )}
      
      {error && (
        <div className="bg-white shadow rounded-lg p-4 border-l-4 border-red-500">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* Products Table */}
      {!loading && !error && (
        <div className="bg-white shadow overflow-hidden rounded-lg">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Produit
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Catégorie
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Prix de vente
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredProduits.length > 0 ? (
                  filteredProduits.map((produit) => {
                    const stockQuantite = getStockQuantite(produit);
                    return (
                      <tr key={produit.id_produit} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                              <Package className="h-5 w-5 text-gray-500" />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{produit.nom}</div>
                              <div className="text-sm text-gray-500">REF: {produit.reference}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{getCategorieLabel(produit.type_stock)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{parseFloat(produit.prix_vente.toString()).toFixed(2)} €</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{stockQuantite}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(stockQuantite)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <Link href={`/products/edit/${produit.id_produit}`} className="text-blue-600 hover:text-blue-900">
                              <Edit className="h-5 w-5" />
                            </Link>
                            <button 
                              className="text-red-600 hover:text-red-900"
                              onClick={() => handleDelete(produit.id_produit)}
                            >
                              <Trash2 className="h-5 w-5" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      Aucun produit trouvé
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Précédent
              </button>
              <button className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Suivant
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Affichage de <span className="font-medium">1</span> à <span className="font-medium">{filteredProduits.length}</span> sur <span className="font-medium">{produits.length}</span> résultats
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span className="sr-only">Précédent</span>
                    &laquo;
                  </button>
                  <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                    1
                  </button>
                  <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span className="sr-only">Suivant</span>
                    &raquo;
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductsList;