import Link from "next/link";
import { Home } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <h2 className="mt-6 text-9xl font-extrabold text-blue-600">404</h2>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Page non trouvée
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            La page que vous recherchez n&apos;existe pas ou a été déplacée.
          </p>
        </div>
        <div className="mt-8">
          <Link
            href="/" // ✅ Utiliser `href` au lieu de `to`
            className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Home className="mr-2 h-5 w-5" />
            Retour à l&apos;accueil
          </Link>
        </div>
      </div>
    </div>
  );
}
