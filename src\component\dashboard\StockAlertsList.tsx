"use client";
import React, { useState, useEffect } from 'react';
import { AlertTriangle } from 'lucide-react';

interface StockAlert {
  produit: string;
  reference: string;
  quantite: number;
  seuil: number;
  emplacement: string;
  entrepot: string;
  status: 'low' | 'critical';
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'low':
      return <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Stock faible</span>;
    case 'critical':
      return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Rupture critique</span>;
    default:
      return null;
  }
};

const StockAlertsList: React.FC = () => {
  const [alerts, setAlerts] = useState<StockAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAlerts = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('accessToken');
        const response = await fetch('http://localhost:8000/api/stock/alertes/', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Erreur de récupération des alertes');
        const data = await response.json();
        // Map backend data to include status
        const formattedAlerts = data.map((alert: StockAlert) => ({
          ...alert,
          status: alert.quantite === 0 ? 'critical' : 'low',
        }));
        setAlerts(formattedAlerts);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur inconnue');
      } finally {
        setLoading(false);
      }
    };
    fetchAlerts();
  }, []);

  if (loading) {
    return <div className="px-4 py-5 text-center text-gray-500">Chargement des alertes...</div>;
  }

  if (error) {
    return <div className="px-4 py-5 text-center text-red-500">Erreur : {error}</div>;
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
          <h3 className="text-lg font-medium leading-6 text-gray-900">Alertes de stock</h3>
        </div>
      </div>

      {alerts.length > 0 ? (
        <ul className="divide-y divide-gray-200">
          {alerts.map((alert, index) => (
            <li key={index} className="px-4 py-4 sm:px-6 hover:bg-gray-50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">{alert.produit}</p>
                  <p className="text-sm text-gray-500">
                    Stock actuel: {alert.quantite} / Min: {alert.seuil} (Emplacement: {alert.emplacement})
                  </p>
                </div>
                <div>
                  {getStatusBadge(alert.status)}
                </div>
              </div>
            </li>
          ))}
        </ul>
      ) : (
        <div className="px-4 py-5 text-center text-gray-500">
          Aucune alerte de stock pour le moment.
        </div>
      )}

      <div className="px-4 py-3 bg-gray-50 text-right sm:px-6 rounded-b-lg">
        <button className="text-sm font-medium text-blue-600 hover:text-blue-500">
          Voir toutes les alertes →
        </button>
      </div>
    </div>
  );
};

export default StockAlertsList;