'use client';
import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Save, ArrowLeft,} from 'lucide-react';

interface ProductFormData {
  nom: string;
  reference: string;
  type_stock: string;   
  prix: string;
  date_peremption: string;
  code_barre: string;
  marque: string;
  description: string;
  unite_mesure: string;
  prix_achat: string;
  prix_vente: string;
  TVA: string;
}

interface ProductResponse {
  id_produit?: string;
  nom?: string;
  prix?: string;
  message?: string;
  errors?: {
    [key: string]: string;
  }
}

const initialFormData: ProductFormData = {
  nom: '',
  reference: '',
  type_stock: '',
  prix: '',
  date_peremption: '',
  code_barre: '',
  marque: '',
  description: '',
  unite_mesure: '',
  prix_achat: '',
  prix_vente: '',
  TVA: '0.2',
};

const TYPE_STOCKS = [
  { value: 'FINIS', label: 'Produit fini' },
  { value: 'MATIERE', label: 'Matière première' },
  { value: 'EMBALLAGE', label: 'Emballage' },
  { value: 'SEMI_FINI', label: 'Produit semi-fini' },
  { value: 'CONSOMMABLE', label: 'Consommable' }
];



const UNITE_CHOICES = [
  { value: 'kg', label: 'Kilogramme' },
  { value: 'g', label: 'Gramme' },
  { value: 'L', label: 'Litre' },
  { value: 'ml', label: 'Millilitre' },
  { value: 'unité', label: 'Unité' }
];

const ProductForm: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string | undefined;
  const [formData, setFormData] = useState<ProductFormData>(initialFormData);
  const [errors, setErrors] = useState<{ [key: string]: string } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const isEditMode = id !== undefined;

  useEffect(() => {
    if (isEditMode) {
      setIsLoading(true);
      // Fetch product data from API
      fetch(`/api/produits/${id}`)
        .then(response => {
          if (!response.ok) {
            throw new Error('Produit non trouvé');
          }
          return response.json();
        })
        .then(data => {
          // Format date_peremption for input date field (YYYY-MM-DD)
          const peremption = new Date(data.date_peremption);
          const formattedDate = peremption.toISOString().split('T')[0];

          setFormData({
            nom: data.nom || '',
            reference: data.reference || '',
            type_stock: data.type_stock || '',
            prix: data.prix?.toString() || '',
            date_peremption: formattedDate || '',
            code_barre: data.code_barre?.toString() || '',
            marque: data.marque || '',
            description: data.description || '',
            unite_mesure: data.unite_mesure || '',
            prix_achat: data.prix_achat?.toString() || '',
            prix_vente: data.prix_vente?.toString() || '',
            TVA: data.TVA?.toString() || '0.2',
          });
          setIsLoading(false);
        })
        .catch(error => {
          console.error('Erreur lors de la récupération du produit:', error);
          setErrors({ general: 'Erreur lors de la récupération du produit' });
          setIsLoading(false);
        });
    }
  }, [id, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors(null);

    // Préparer les données à envoyer
    const dataToSend = {
      ...formData,
      prix: parseFloat(formData.prix),
      prix_achat: parseFloat(formData.prix_achat),
      prix_vente: parseFloat(formData.prix_vente),
      TVA: parseFloat(formData.TVA),
      code_barre: parseInt(formData.code_barre, 10)
    };

    try {
      const response = await fetch(
        'http://localhost:8000/api/produits/getAllProducts/', 
        {
          method:"POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(dataToSend),
        }
      );
      
      const data: ProductResponse = await response.json();

      if (!response.ok) {
        if (data.errors) {
          setErrors(data.errors);
        } else {
          throw new Error(data.message || "Erreur lors de l'opération");
        }
        setIsLoading(false);
        return;
      }
      
      router.push('/products/productList');
    } catch (err) {
      setErrors({
        general: err instanceof Error ? err.message : "Une erreur est survenue"
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {isEditMode ? 'Modifier le produit' : 'Ajouter un produit'}
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            {isEditMode
              ? 'Modifiez les informations du produit existant'
              : 'Créez un nouveau produit dans votre catalogue'}
          </p>
        </div>
        <button
          onClick={() => router.push('/products/productList')}
          className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-8">
          {errors && errors.general && (
            <div className="bg-red-50 border-l-4 border-red-400 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{errors.general}</p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-white shadow overflow-hidden rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                {/* Informations de base */}
                <div className="sm:col-span-6">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">Informations de base</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Informations essentielles du produit.
                  </p>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="nom" className="block text-sm font-medium text-gray-700">
                    Nom du produit *
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      name="nom"
                      id="nom"
                      required
                      value={formData.nom}
                      onChange={handleChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${errors?.nom ? 'border-red-300' : ''}`}
                    />
                    {errors?.nom && <p className="mt-1 text-sm text-red-600">{errors.nom}</p>}
                  </div>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="reference" className="block text-sm font-medium text-gray-700">
                    Référence *
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      name="reference"
                      id="reference"
                      required
                      value={formData.reference}
                      onChange={handleChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${errors?.reference ? 'border-red-300' : ''}`}
                    />
                    {errors?.reference && <p className="mt-1 text-sm text-red-600">{errors.reference}</p>}
                  </div>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="type_stock" className="block text-sm font-medium text-gray-700">
                    Type de stock *
                  </label>
                  <div className="mt-1">
                    <select
                      id="type_stock"
                      name="type_stock"
                      required
                      value={formData.type_stock}
                      onChange={handleChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${errors?.categorie ? 'border-red-300' : ''}`}
                    >
                      <option value="">Sélectionner le type de stock</option>
                      {TYPE_STOCKS.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                    {errors?.type_stock && <p className="mt-1 text-sm text-red-600">{errors.type_stock}</p>}
                  </div>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="marque" className="block text-sm font-medium text-gray-700">
                    Marque *
                  </label>
                  <div className="mt-1">
                    <input
                      type="text"
                      name="marque"
                      id="marque"
                      required
                      value={formData.marque}
                      onChange={handleChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${errors?.marque ? 'border-red-300' : ''}`}
                    />
                    {errors?.marque && <p className="mt-1 text-sm text-red-600">{errors.marque}</p>}
                  </div>
                </div>

                <div className="sm:col-span-6">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="description"
                      name="description"
                      rows={3}
                      value={formData.description}
                      onChange={handleChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${errors?.description ? 'border-red-300' : ''}`}
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Description détaillée du produit.
                  </p>
                </div>

                {/* Prix et inventaire */}
                <div className="sm:col-span-6 pt-5 border-t border-gray-200">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">Prix et informations supplémentaires</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Informations sur le prix et les caractéristiques du produit.
                  </p>
                </div>

                <div className="sm:col-span-2">
                  <label htmlFor="prix_achat" className="block text-sm font-medium text-gray-700">
                    Prix achat (€) *
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="number"
                      name="prix_achat"
                      id="prix_achat"
                      required
                      min="0"
                      step="0.01"
                      value={formData.prix_achat}
                      onChange={handleChange}
                      className={`focus:ring-blue-500 focus:border-blue-500 block w-full pr-12 sm:text-sm border-gray-300 rounded-md ${errors?.prix_achat ? 'border-red-300' : ''}`}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">€</span>
                    </div>
                    {errors?.prix_achat && <p className="mt-1 text-sm text-red-600">{errors.prix_achat}</p>}
                  </div>
                </div>

                <div className="sm:col-span-2">
                  <label htmlFor="prix_vente" className="block text-sm font-medium text-gray-700">
                    Prix de vente (€) *
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="number"
                      name="prix_vente"
                      id="prix_vente"
                      required
                      min="0"
                      step="0.01"
                      value={formData.prix_vente}
                      onChange={handleChange}
                      className={`focus:ring-blue-500 focus:border-blue-500 block w-full pr-12 sm:text-sm border-gray-300 rounded-md ${errors?.prix_vente ? 'border-red-300' : ''}`}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">€</span>
                    </div>
                    {errors?.prix_vente && <p className="mt-1 text-sm text-red-600">{errors.prix_vente}</p>}
                  </div>
                </div>

                <div className="sm:col-span-2">
                  <label htmlFor="prix" className="block text-sm font-medium text-gray-700">
                    Prix (€) *
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="number"
                      name="prix"
                      id="prix"
                      required
                      min="0"
                      step="0.01"
                      value={formData.prix}
                      onChange={handleChange}
                      className={`focus:ring-blue-500 focus:border-blue-500 block w-full pr-12 sm:text-sm border-gray-300 rounded-md ${errors?.prix ? 'border-red-300' : ''}`}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">€</span>
                    </div>
                    {errors?.prix && <p className="mt-1 text-sm text-red-600">{errors.prix}</p>}
                  </div>
                </div>

                <div className="sm:col-span-2">
                  <label htmlFor="TVA" className="block text-sm font-medium text-gray-700">
                    Taux de TVA
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="number"
                      name="TVA"
                      id="TVA"
                      min="0"
                      max="1"
                      step="0.01"
                      value={formData.TVA}
                      onChange={handleChange}
                      className={`focus:ring-blue-500 focus:border-blue-500 block w-full pr-12 sm:text-sm border-gray-300 rounded-md ${errors?.TVA ? 'border-red-300' : ''}`}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">(0.2 = 20%)</span>
                    </div>
                    {errors?.TVA && <p className="mt-1 text-sm text-red-600">{errors.TVA}</p>}
                  </div>
                </div>

                <div className="sm:col-span-2">
                  <label htmlFor="date_peremption" className="block text-sm font-medium text-gray-700">
                    Date de péremption *
                  </label>
                  <div className="mt-1">
                    <input
                      type="date"
                      name="date_peremption"
                      id="date_peremption"
                      required
                      value={formData.date_peremption}
                      onChange={handleChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${errors?.date_peremption ? 'border-red-300' : ''}`}
                    />
                    {errors?.date_peremption && <p className="mt-1 text-sm text-red-600">{errors.date_peremption}</p>}
                  </div>
                </div>

                <div className="sm:col-span-2">
                  <label htmlFor="unite_mesure" className="block text-sm font-medium text-gray-700">
                    Unité de mesure *
                  </label>
                  <div className="mt-1">
                    <select
                      id="unite_mesure"
                      name="unite_mesure"
                      required
                      value={formData.unite_mesure}
                      onChange={handleChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${errors?.unite_mesure ? 'border-red-300' : ''}`}
                    >
                      <option value="">Sélectionner une unité</option>
                      {UNITE_CHOICES.map((unite) => (
                        <option key={unite.value} value={unite.value}>
                          {unite.label}
                        </option>
                      ))}
                    </select>
                    {errors?.unite_mesure && <p className="mt-1 text-sm text-red-600">{errors.unite_mesure}</p>}
                  </div>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="code_barre" className="block text-sm font-medium text-gray-700">
                    Code-barres *
                  </label>
                  <div className="mt-1">
                    <input
                      type="number"
                      name="code_barre"
                      id="code_barre"
                      required
                      value={formData.code_barre}
                      onChange={handleChange}
                      className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md ${errors?.code_barre ? 'border-red-300' : ''}`}
                    />
                    {errors?.code_barre && <p className="mt-1 text-sm text-red-600">{errors.code_barre}</p>}
                  </div>
                </div>
              </div>
            </div>
            <div className="px-4 py-3 bg-gray-50 text-right sm:px-6 flex justify-between">
             
              <div>
                <button
                  type="button"
                  onClick={() => router.push('/products/productList')}
                  className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Save className="mr-2 h-4 w-4" />
                  {isEditMode ? 'Mettre à jour' : 'Enregistrer'}
                </button>
              </div>
            </div>
          </div>
        </form>
      )}
    </div>
  );
};

export default ProductForm;