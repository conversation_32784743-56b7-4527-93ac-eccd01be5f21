"use client";
import { useState, useEffect } from 'react';
import { Package, ShoppingCart, AlertTriangle, TrendingUp } from 'lucide-react';
import StatCard from '@/component/dashboard/StatCard';
import RecentActivity from '@/component/dashboard/RecentActivity';
import StockAlertsList from '@/pages/stock/stockAlert';
import SalesChart from '@/component/dashboard/SalesChart';
import TopProducts from '@/component/dashboard/TopProducts';
import QuickActions from '@/component/dashboard/QuickActions';

interface Users {
  id: number;
  nom: string;
  prenom: string;
  email: string;
}

interface Alert {
  produit: string;
  reference: string;
  quantite: number;
  seuil: number;
  emplacement: string;
  entrepot: string;
}

interface Produit {
  id_produit : string ;
  nom : string ;
  categorie:string;
}


const Dashboard: React.FC = () => {
  const [users, setUsers] = useState<Users | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [produits, setProduits] = useState<Produit[]>([]);

  // Fetch user data
  useEffect(() => {
    const name_user = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`http://localhost:8000/api/users/user/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Erreur de récupération');
        const data = await response.json();
        setUsers(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur inconnue');
      } finally {
        setLoading(false);
      }
    };
    name_user();
  }, []);

  // Fetch stock alerts
  useEffect(() => {
    const alert_stock = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`http://localhost:8000/api/stock/alertes/`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Erreur de récupération');
        const data = await response.json();
        setAlerts(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur inconnue');
      } finally {
        setLoading(false);
      }
    };
    alert_stock();
  }, []);

  //fetch number produit dans le stock
  useEffect(() => {
    const fetchProduits = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://localhost:8000/api/produits/getAllProducts/');
        if (!response.ok) {
          throw new Error('Erreur lors du chargement des produits');
        }
        const data = await response.json();
        setProduits(data);
        setLoading(false);
      } catch (err) {
        setError('Erreur lors du chargement des produits');
        setLoading(false);
        console.error('Erreur de chargement:', err);
      }
    };
    
    fetchProduits();
  }, []);

if (loading) return <p>Chargement...</p>;
if (error) return <p className="text-red-500">Erreur : {error}</p>;
if (!users) return <p>Aucun utilisateur connecté</p>;
if (!alerts) return <p>Aucun stock récupéré</p>;

return (
  <div className="space-y-6">
    <div>
      <h1 className="text-2xl font-bold text-gray-900">Tableau de bord</h1>
      <p className="mt-1 text-sm text-gray-500">
        Bienvenue,<span className="font-bold uppercase"> {users.prenom} {users.nom}</span>. Voici un aperçu de votre activité.
      </p>
    </div>

    {/* Stats Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <StatCard
        title="Niveau des stocks"
        value={`${produits.length} produits`}
        icon={<Package size={24} />}
        color="blue"
        trend={{ value: produits.length , isPositive: true }}
      />
      <StatCard
        title="Ventes du mois"
        value="24,500 €"
        icon={<ShoppingCart size={24} />}
        color="green"
        trend={{ value: 12, isPositive: true }}
      />
      <StatCard
        title="Alertes de stock"
        value={`${alerts.length} alertes`}
        icon={<AlertTriangle size={24} />}
        color="red"
        trend={{ value: alerts.length, isPositive: false }}
      />
      <StatCard
        title="Croissance"
        value="+15%"
        icon={<TrendingUp size={24} />}
        color="yellow"
        trend={{ value: 7, isPositive: true }}
      />
    </div>

    {/* Quick Actions */}
    <QuickActions />

    {/* Charts & Lists */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <SalesChart />
      <TopProducts />
    </div>

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <RecentActivity />
      <StockAlertsList />
    </div>
  </div>
);
};

export default Dashboard;