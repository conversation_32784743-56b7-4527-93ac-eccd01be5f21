import { motion } from 'framer-motion';
import { 
  HelpCircle, 
  Book, 
  MessageCircle, 
  Phone, 
  Mail, 
  Video,
  FileText,
  ExternalLink
} from 'lucide-react';

const Aide: React.FC = () => {
  const faqs = [
    {
      question: "Comment ajouter un nouveau produit ?",
      answer: "Pour ajouter un nouveau produit, cliquez sur 'Produits' dans le menu latéral, puis sur le bouton 'Ajouter un produit'. Remplissez tous les champs requis dans le formulaire et cliquez sur 'Enregistrer'."
    },
    {
      question: "Comment gérer les alertes de stock ?",
      answer: "Les alertes de stock sont configurables dans les paramètres de chaque produit. Définissez un seuil minimal et vous recevrez des notifications lorsque le stock atteint ce niveau."
    },
    {
      question: "Comment créer une commande fournisseur ?",
      answer: "Accédez à la section 'Fournisseurs', cliquez sur 'Commandes fournisseurs', puis sur 'Nouvelle commande'. Sélectionnez le fournisseur et ajoutez les produits souhaités."
    }
  ];

  const resources = [
    {
      title: "Guide de démarrage",
      description: "Apprenez les bases de l'utilisation de StockMaster",
      icon: <Book className="h-6 w-6" />,
      link: "#"
    },
    {
      title: "Tutoriels vidéo",
      description: "Regardez nos tutoriels pas à pas",
      icon: <Video className="h-6 w-6" />,
      link: "#"
    },
    {
      title: "Documentation",
      description: "Consultez notre documentation détaillée",
      icon: <FileText className="h-6 w-6" />,
      link: "#"
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="space-y-6"
    >
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Centre d&apos;aide</h1>
        <p className="mt-1 text-sm text-gray-500">
          Trouvez de l&apos;aide et des ressources pour utiliser StockMaster
        </p>
      </div>

      {/* Quick Help Section */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        <div className="bg-blue-50 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <MessageCircle className="h-6 w-6 text-blue-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">Chat en direct</h2>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Discutez avec notre équipe de support en temps réel
          </p>
          <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            Démarrer le chat
          </button>
        </div>

        <div className="bg-green-50 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Phone className="h-6 w-6 text-green-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">Support téléphonique</h2>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Appelez-nous au +261 33 48 186 78
          </p>
          <p className="text-xs text-gray-500">
            Du lundi au vendredi, 9h-18h
          </p>
        </div>

        <div className="bg-purple-50 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Mail className="h-6 w-6 text-purple-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">Email</h2>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Envoyez-nous un email à <EMAIL>
          </p>
          <p className="text-xs text-gray-500">
            Réponse sous 24h ouvrées
          </p>
        </div>
      </motion.div>

      {/* Resources Section */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow rounded-lg overflow-hidden"
      >
        <div className="p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Ressources</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {resources.map((resource, index) => (
              <a
                key={index}
                href={resource.link}
                className="block p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              >
                <div className="flex items-center mb-3">
                  <div className="text-blue-600">
                    {resource.icon}
                  </div>
                  <ExternalLink className="h-4 w-4 text-gray-400 ml-2" />
                </div>
                <h3 className="text-sm font-medium text-gray-900">{resource.title}</h3>
                <p className="mt-1 text-sm text-gray-500">{resource.description}</p>
              </a>
            ))}
          </div>
        </div>
      </motion.div>

      {/* FAQ Section */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow rounded-lg overflow-hidden"
      >
        <div className="p-6">
          <div className="flex items-center mb-4">
            <HelpCircle className="h-6 w-6 text-blue-600 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">Questions fréquentes</h2>
          </div>
          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <div key={index} className="border-b border-gray-200 pb-6 last:border-0 last:pb-0">
                <h3 className="text-sm font-medium text-gray-900 mb-2">{faq.question}</h3>
                <p className="text-sm text-gray-500">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Contact Form */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white shadow rounded-lg overflow-hidden"
      >
        <div className="p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Nous contacter</h2>
          <form className="space-y-4">
            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                Sujet
              </label>
              <select
                id="subject"
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option>Question technique</option>
                <option>Problème de compte</option>
                <option>Suggestion</option>
                <option>Autre</option>
              </select>
            </div>
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700">
                Message
              </label>
              <textarea
                id="message"
                rows={4}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Décrivez votre problème ou votre question..."
              />
            </div>
            <div className="flex justify-end">
              <button
                type="submit"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                Envoyer le message
              </button>
            </div>
          </form>
        </div>

      </motion.div>
    </motion.div>
  );
};

export default Aide;