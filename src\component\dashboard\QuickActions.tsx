import React from 'react';
import Link from "next/link";
import { 
  Plus, 
  ShoppingCart, 
  Truck, 
  FileBarChart, 
  Package,
  AlertTriangle
} from 'lucide-react';

const QuickActions: React.FC = () => {
  const actions = [
    { 
      name: 'Nouveau produit', 
      icon: <Plus size={20} />, 
      path: '/products/productForm',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    { 
      name: 'Nouvelle vente', 
      icon: <ShoppingCart size={20} />, 
      path: '/commande/SalesHistory',
      color: 'bg-green-500 hover:bg-green-600'
    },
    { 
      name: 'Commander', 
      icon: <Truck size={20} />, 
      path: '/suppliers/orders',
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    { 
      name: 'Inventaire', 
      icon: <Package size={20} />, 
      path: '/stock/movements',
      color: 'bg-yellow-500 hover:bg-yellow-600'
    },
    { 
      name: 'Alertes', 
      icon: <AlertTriangle size={20} />, 
      path: '/stocks/stockAlerte',
      color: 'bg-red-500 hover:bg-red-600'
    },
    { 
      name: 'Rapports', 
      icon: <FileBarChart size={20} />, 
      path: '/reports',
      color: 'bg-indigo-500 hover:bg-indigo-600'
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Actions rapides</h3>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3">
        {actions.map((action, index) => (
          <Link
            key={index}
            href={action.path}
            className={`${action.color} text-white rounded-lg p-3 flex flex-col items-center justify-center text-center transition-colors duration-200`}
          >
            <span className="mb-1">{action.icon}</span>
            <span className="text-xs font-medium">{action.name}</span>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default QuickActions;