"use client";
import React, { useState, useEffect } from 'react';
import {  Filter, Calendar, Download } from 'lucide-react';


interface StockMovement {
  id_mouvement: string;
  date_mouvement: string;
  produit: {
    nom: string;
    reference: string;
  };
  type_mouvement: 'ENTREE' | 'SORTIE' | 'TRANSFERT' | 'AJUSTEMENT';
  quantite: number;
  notes: string;
  utilisateur: string;
  location: {
    nom: string;
    entrepot: {
      nom: string;
    };
  };
}

const StockMovements: React.FC = () => {
  const [movements, setMovements] = useState<StockMovement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [movementType, setMovementType] = useState<string>('');
  const [dateRange, setDateRange] = useState<string>('');
 
  // Fetch movements from API
  useEffect(() => {
    const fetchMovements = async () => {
      try {
        const token = localStorage.getItem('accessToken');
        // an API endpoint that returns all movements
        const response = await fetch('http://127.0.0.1:8000/api/stock/mouvements/', {
          headers: {
            'Content-Type': 'application/json',
            // Add authentication here if needed
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des mouvements de stock');
        }

        const data = await response.json();
        setMovements(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      } finally {
        setLoading(false);
      }
    };

    fetchMovements();
  }, []);

  // Open modal for adding stock
 

  // Handle successful stock addition
 

  // Filter movements based on type and date
  const filteredMovements = movements.filter(movement => {
    if (movementType && movement.type_mouvement !== movementType) return false;
    
    // Add date filtering logic if needed
    if (dateRange) {
      const movementDate = new Date(movement.date_mouvement);
      const today = new Date();
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay());
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      
      if (dateRange === 'today') {
        if (movementDate.toDateString() !== today.toDateString()) return false;
      } else if (dateRange === 'week') {
        if (movementDate < startOfWeek) return false;
      } else if (dateRange === 'month') {
        if (movementDate < startOfMonth) return false;
      }
    }
    
    return true;
  });

  if (loading) {
    return <div className="flex justify-center p-8">Chargement des mouvements de stock...</div>;
  }

  if (error) {
    return <div className="bg-red-50 p-4 rounded-md text-red-800">{error}</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Mouvements de stock</h1>
          <p className="mt-1 text-sm text-gray-500">
            Historique des entrées et sorties de stock
          </p>
        </div>
        <div className="flex space-x-2">
          
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter className="h-5 w-5 text-gray-400" />
            </div>
            <select
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={movementType}
              onChange={(e) => setMovementType(e.target.value)}
            >
              <option value="">Tous les mouvements</option>
              <option value="ENTREE">Entrées</option>
              <option value="SORTIE">Sorties</option>
              <option value="TRANSFERT">Transferts</option>
              <option value="AJUSTEMENT">Ajustements</option>
            </select>
          </div>

          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Calendar className="h-5 w-5 text-gray-400" />
            </div>
            <select
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
            >
              <option value="">Toutes les dates</option>
              <option value="today">Aujourd&apos;hui</option>
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
            </select>
          </div>

          <div className="flex justify-end">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <Download className="mr-2 h-4 w-4" />
              Exporter
            </button>
          </div>
        </div>
      </div>

      {/* Movements Table */}
      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Produit
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantité
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Emplacement
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Notes
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Utilisateur
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMovements.length > 0 ? (
                filteredMovements.map((movement) => (
                  <tr key={movement.id_mouvement} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(movement.date_mouvement).toLocaleString('fr-FR')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{movement.produit.nom}</div>
                      <div className="text-xs text-gray-500">{movement.produit.reference}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {movement.type_mouvement === 'ENTREE' ? (
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Entrée
                        </span>
                      ) : movement.type_mouvement === 'SORTIE' ? (
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                          Sortie
                        </span>
                      ) : movement.type_mouvement === 'TRANSFERT' ? (
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                          Transfert
                        </span>
                      ) : (
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                          Ajustement
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {movement.quantite}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {movement.location.nom} ({movement.location.entrepot.nom})
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {movement.notes || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {movement.utilisateur}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    Aucun mouvement de stock trouvé
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {filteredMovements.length > 0 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Affichage de <span className="font-medium">1</span> à <span className="font-medium">{filteredMovements.length}</span> résultats
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

     
    </div>
  );
};

export default StockMovements;