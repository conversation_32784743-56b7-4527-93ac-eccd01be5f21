"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";


export default function Page() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
    
  useEffect(() => {
    // code s'exécute uniquement côté client
    const token = localStorage.getItem("userToken");
        
    if (!token) {
      router.push("/users/login");
    } else {
      setIsAuthenticated(true);
    }
    setIsLoading(false);
  }, [router]);
    
  // Afficher un état de chargement pendant la vérification
  if (isLoading) return null;
    
  // Affiche le Dashboard uniquement si l'utilisateur est authentifié
  return isAuthenticated ? router.push("/accueil") : null;
}