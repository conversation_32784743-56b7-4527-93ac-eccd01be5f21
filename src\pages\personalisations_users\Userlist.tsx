"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from 'next/image';
import { Search, Filter, Edit, Trash2, ArrowUpDown, User, PlusCircle, Frown } from 'lucide-react';

// Interface typée pour les données utilisateur
interface Get_User {
  id_utilisateur: string;
  username: string;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  adresse: string;
  role: 'ADMIN' | 'VENDEUR' | 'FOURNISSEUR' | 'RESPONSABLE_ENTREPOT';
  createdAt?: string;
  statut: boolean;
  avatar_url?: string;
}

export default function UserList() {
  // États du composant
  const [users, setUsers] = useState<Get_User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [isMobile, setIsMobile] = useState(false);

  // Détection de la taille de l'écran
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Récupération des utilisateurs
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch('http://localhost:8000/api/users/list-users/', {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        console.log(response)
        if (!response.ok) throw new Error('Erreur de chargement');
        setUsers(await response.json());
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur inconnue');
      } finally {
        setLoading(false);
      }

    };
    fetchUsers();
   
  }, []);

  // Suppression d'un utilisateur
  const handleDelete = async (id: string) => {
    if (!confirm('Supprimer cet utilisateur ?')) return;
    
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`http://localhost:8000/api/auth/delete/${id}/`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` },
      });
      if (!response.ok) throw new Error('Échec de la suppression');
      setUsers(users.filter(user => user.id_utilisateur !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur de la suppression');
    }
  };

  // Filtrage des utilisateurs
  const filteredUsers = users.filter(user => {
    const searchMatch = `${user.nom} ${user.prenom} ${user.telephone} ${user.role}`
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const roleMatch = !selectedRole || user.role === selectedRole;
    return searchMatch && roleMatch;
  });

  // Traduction des rôles
  const translateRole = (role: string) => ({
    'ADMIN': 'Admin',
    'VENDEUR': 'Vendeur',
    'FOURNISSEUR': 'Fournisseur',
    'RESPONSABLE_ENTREPOT': 'Responsable',
  }[role] || role);

  return (
    <div className="p-4 md:p-6">
      {/* En-tête */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <User className="text-blue-600" size={24} />
            Gestion des Utilisateurs
          </h1>
          <p className="text-sm text-gray-500 mt-1">
            {users.length} utilisateur{users.length !== 1 ? 's' : ''} enregistré{users.length !== 1 ? 's' : ''}
          </p>
        </div>
        
        <Link href="/users/create" className="btn-primary flex items-center gap-2 w-full md:w-auto">
          <PlusCircle size={18} />
          <span>Nouvel utilisateur</span>
        </Link>
      </div>

      {/* Barre de filtres */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {/* Champ de recherche */}
          <div className="relative">
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Search className="text-gray-400" size={18} />
            </div>
            <input
              type="text"
              placeholder="Rechercher..."
              className="pl-10 pr-4 py-2 w-full border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filtre par rôle */}
          <div className="relative">
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Filter className="text-gray-400" size={18} />
            </div>
            <select
              className="pl-10 pr-4 py-2 w-full border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
            >
              <option value="">Tous les rôles</option>
              {Array.from(new Set(users.map(u => u.role))).map(role => (
                <option key={role} value={role}>{translateRole(role)}</option>
              ))}
            </select>
          </div>

          {/* Bouton de tri */}
          <button className="btn-secondary flex items-center justify-center gap-2">
            <ArrowUpDown size={16} />
            <span>Trier</span>
          </button>
        </div>
      </div>

      {/* États de chargement/erreur */}
      {loading && (
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-100 rounded-lg animate-pulse" />
          ))}
        </div>
      )}

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded-r-lg">
          <p className="text-red-700 flex items-center gap-2">
            <Frown size={18} />
            {error}
          </p>
        </div>
      )}

      {/* Contenu principal */}
      {!loading && !error && (
        <>
          {/* Version mobile */}
          {isMobile ? (
            <div className="space-y-3">
              {filteredUsers.length > 0 ? (
                filteredUsers.map(user => (
                  <div key={user.id_utilisateur} className="bg-white p-4 rounded-lg shadow">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0">
                        {user?.avatar_url ? (
                          <Image
                            src={user.avatar_url}
                            alt={`${user.prenom} ${user.nom}`}
                            width={100}
                            height={100}
                            className="rounded-full"
                          />
                        ) : (
                          <div className="bg-gray-200 rounded-full p-2">
                            <User className="text-gray-500" size={24} />
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <h3 className="font-medium">{user.prenom} {user.nom}</h3>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            user.statut ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {user.statut ? 'Actif' : 'Inactif'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">{user.email}</p>
                        <p className="text-sm">{user.telephone}</p>
                        <div className="mt-2 flex justify-between items-center">
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {translateRole(user.role)}
                          </span>
                          <div className="flex gap-2">
                            <Link href={`/users/edit/${user.id_utilisateur}`} className="text-blue-600">
                              <Edit size={18} />
                            </Link>
                            <button onClick={() => handleDelete(user.id_utilisateur)} className="text-red-600">
                              <Trash2 size={18} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="bg-white p-6 rounded-lg shadow text-center">
                  <Frown className="mx-auto text-gray-400" size={32} />
                  <p className="mt-2 text-gray-600">Aucun utilisateur trouvé</p>
                </div>
              )}
            </div>
          ) : (
            /* Version desktop */
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Utilisateur
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Rôle
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Contact
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredUsers.length > 0 ? (
                      filteredUsers.map(user => (
                        <tr key={user.id_utilisateur} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                {user?.avatar_url ? (
                                  <Image
                                    src={user.avatar_url}
                                    alt={`${user.prenom} ${user.nom}`}
                                    width={100}
                                    height={100}
                                    className="rounded-full"
                                  />
                                ) : (
                                  <div className="bg-gray-200 rounded-full h-10 w-10 flex items-center justify-center">
                                    <User className="text-gray-500" size={20} />
                                  </div>
                                )}
                              </div>
                              <div className="ml-4">
                                <div className="font-medium text-gray-900">
                                  {user.prenom} {user.nom}
                                </div>
                                <div className="text-sm text-gray-500">{user.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                              {translateRole(user.role)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {user.telephone}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              user.statut ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {user.statut ? 'Actif' : 'Inactif'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex justify-end space-x-3">
                              <Link
                                href={`/users/edit/${user.id_utilisateur}`}
                                className="text-blue-600 hover:text-blue-900 flex items-center gap-1"
                              >
                                <Edit size={16} />
                                <span className="hidden md:inline">Éditer</span>
                              </Link>
                              <button
                                onClick={() => handleDelete(user.id_utilisateur)}
                                className="text-red-600 hover:text-red-900 flex items-center gap-1"
                              >
                                <Trash2 size={16} />
                                <span className="hidden md:inline">Supprimer</span>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 text-center">
                          <div className="flex flex-col items-center justify-center py-6">
                            <Frown className="text-gray-400 mb-2" size={32} />
                            <p className="text-gray-600">Aucun utilisateur correspondant</p>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}