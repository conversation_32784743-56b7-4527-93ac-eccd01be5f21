

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-white border-t border-gray-200 py-4">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-gray-500">
            &copy; {currentYear} StockMaster. Tous droits réservés.
          </div>
          <div className="mt-2 md:mt-0 flex space-x-4 text-sm text-gray-500">
            <a href="#" className="hover:text-blue-600">Mentions légales</a>
            <a href="#" className="hover:text-blue-600">Confidentialité</a>
            <a href="#" className="hover:text-blue-600">Documentation</a>
            <a href="#" className="hover:text-blue-600">Support</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;