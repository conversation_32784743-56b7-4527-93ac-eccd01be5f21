'use client';
import Image from 'next/image';
import { X } from 'lucide-react';

type Props = {
  file: File;
  remove: () => void;
};

export default function ClientOnlyImage({ file, remove }: Props) {
  const previewUrl = URL.createObjectURL(file);

  return (
    <div className="relative">
      <Image
        src={previewUrl}
        className="w-20 h-20 rounded-full object-cover"
        alt="Preview"
        width={80}
        height={80}
      />
      <button
        type="button"
        onClick={remove}
        className="absolute -top-2 -right-2 bg-red-500 rounded-full p-1"
      >
        <X className="h-4 w-4 text-white" />
      </button>
    </div>
  );
}
