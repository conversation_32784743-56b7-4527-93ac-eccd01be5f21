import Link from "next/link";
import {
  LayoutDashboard,
  Package,
  BarChart3,
  ShoppingCart,
  Truck,
  FileBarChart,
  Settings,
  HelpCircle,
  X,
  PackageOpen
} from "lucide-react";

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, toggleSidebar }) => {
  const navItems = [
    { name: "Tableau de Bord", path: "/accueil", icon: <LayoutDashboard size={20} /> },
    
    {
      name: "Produits",
      path: "/products/productList",
      icon: <Package size={20} />,
      subItems: [
        { name: "Liste des produits", path: "/products/productList" },
        { name: "Ajouter un produit", path: "/products/productForm" }
      ]
    },
    {
      name: "Stocks",
      path: "/stocks/stockMouvement",
      icon: <PackageOpen size={20} />,
      subItems: [
        { name: "Mouvements de stock", path: "/stocks/stockMouvement" },
        { name: "Alertes de stock", path: "/stocks/stockAlerte" }
      ]
    },
    {
      name: "Ventes",
      path: "/sales",
      icon: <ShoppingCart size={20} />,
      subItems: [
        { name: "Commandes clients", path: "/commande/orderList" },
        { name: "Historique des ventes", path: "/commande/SalesHistory" }
      ]
    },
    {
      name: "Fournisseurs",
      path: "/Sup",
      icon: <Truck size={20} />,
      subItems: [
        { name: "Liste des fournisseurs", path: "/Sup" },
        { name: "Commandes fournisseurs", path: "/Sup/supOrder" }
      ]
    },
    { name: "Rapports", path: "/reports", icon: <FileBarChart size={20} /> },
    {
      name: "Paramètres",
      path: "/parameter",
      icon: <Settings size={20} />,
      subItems: [
        { name: "Configuration générale", path: "/parameter" },
        { name: "Gestion des utilisateurs", path: "/gestion_user" }
      ]
    },
    { name: "Aide", path: "/help", icon: <HelpCircle size={20} /> },
  ];

  return (
    <>
      {/* Mobile sidebar backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/2 bg-opacity-50 lg:hidden"
          onClick={toggleSidebar}
        ></div>
      )}

      {/* Sidebar */}
      <aside
        className={`fixed inset-y-0 left-0 z-30 w-64 transform bg-blue-800 text-white transition duration-300 ease-in-out lg:static lg:translate-x-0 ${isOpen ? "translate-x-0" : "-translate-x-full"
          }`}
      >
        <div className="flex items-center justify-between h-16 px-4 border-b border-blue-700">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 mr-2" />
            <span className="text-xl font-semibold">StockMaster</span>
          </div>
          <button
            onClick={toggleSidebar}
            className="p-1 rounded-md hover:bg-blue-700 lg:hidden"
          >
            <X size={24} />
          </button>
        </div>

        <nav className="mt-5 px-2">
          <div className="space-y-1">
            {navItems.map((item) => (
              <div key={item.path} className="mb-2">
                <Link
                  href={item.path}
                  className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors text-blue-100 hover:bg-blue-700"
                >
                  <span className="mr-3">{item.icon}</span>
                  {item.name}
                </Link>

                {item.subItems && (
                  <div className="ml-8 mt-1 space-y-1">
                    {item.subItems.map((subItem) => (
                      <Link
                        key={subItem.path}
                        href={subItem.path}
                        className="block px-3 py-1 text-sm rounded-md transition-colors text-blue-100 hover:bg-blue-600"
                      >
                        {subItem.name}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </nav>
      </aside>
    </>
  );
};

export default Sidebar;
