"use client";
import React, { useState, useEffect } from 'react';
import { AlertTriangle, Search,  Bell, Download } from 'lucide-react';

interface StockAlert {
  produit: string;
  reference: string;
  quantite: number;
  seuil: number;
  emplacement: string;
  entrepot: string;
}

const StockAlerts: React.FC = () => {
  const [alerts, setAlerts] = useState<StockAlert[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch alerts from API
  useEffect(() => {
    const fetchAlerts = async () => {
      try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch('http://127.0.0.1:8000/api/stock/alertes/', {
          headers: {
            'Content-Type': 'application/json',
            // Ajoutez votre logique d'authentification ici si nécessaire
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des alertes');
        }

        const data = await response.json();
        setAlerts(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      } finally {
        setLoading(false);
      }
    };

    fetchAlerts();
  }, []);

  // Filter alerts based on search term
  const filteredAlerts = alerts.filter(alert =>
    alert.produit.toLowerCase().includes(searchTerm.toLowerCase()) ||
    alert.reference.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Count alerts by type
  const criticalCount = alerts.filter(a => a.quantite === 0).length;
  const lowCount = alerts.filter(a => a.quantite > 0 && a.quantite < a.seuil).length;
  const totalCount = alerts.length;

  // Status badge helper function
  const getStatusBadge = (quantite: number, seuil: number) => {
    if (quantite === 0) {
      return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Rupture critique</span>;
    } else if (quantite < seuil) {
      return <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Stock faible</span>;
    }
    return null;
  };

  // Status color helper function
  const getStatusColor = (quantite: number, seuil: number) => {
    if (quantite === 0) {
      return 'text-red-600';
    } else if (quantite < seuil) {
      return 'text-yellow-600';
    }
    return 'text-gray-600';
  };

  if (loading) {
    return <div className="flex justify-center p-8">Chargement des alertes de stock...</div>;
  }

  if (error) {
    return <div className="bg-red-50 p-4 rounded-md text-red-800">{error}</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Alertes de stock</h1>
          <p className="mt-1 text-sm text-gray-500">
            Gérez vos alertes de rupture de stock
          </p>
        </div>
        <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <Download className="mr-2 h-4 w-4" />
          Exporter
        </button>
      </div>

      {/* Alert Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-red-50 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-red-100 rounded-full">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-red-800">Ruptures critiques</h2>
              <p className="text-2xl font-semibold text-red-900">{criticalCount} produits</p>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0 p-3 bg-yellow-100 rounded-full">
              <Bell className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-yellow-800">Stocks faibles</h2>
              <p className="text-2xl font-semibold text-yellow-900">{lowCount} produits</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Rechercher un produit..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Alerts Table */}
      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Produit
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock actuel
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Seuil d`|apos alerte
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Emplacement
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Entrepôt
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAlerts.length > 0 ? (
                filteredAlerts.map((alert, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{alert.produit}</div>
                      <div className="text-xs text-gray-500">{alert.reference}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm font-medium ${getStatusColor(alert.quantite, alert.seuil)}`}>
                        {alert.quantite}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {alert.seuil}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {alert.emplacement}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {alert.entrepot}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(alert.quantite, alert.seuil)}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                    Aucune alerte de stock trouvée
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {filteredAlerts.length > 0 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Affichage de <span className="font-medium">1</span> à <span className="font-medium">{filteredAlerts.length}</span> sur <span className="font-medium">{totalCount}</span> résultats
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StockAlerts;