// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Définition des routes qui ne nécessitent pas d'authentification
const publicPaths = ['/users/login', '/users/register'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Vérifier si le chemin actuel est public
  const isPublicPath = publicPaths.some(path => pathname.startsWith(path));
  
  // Récupérer le token dans les cookies
  const token = request.cookies.get('accessToken')?.value;
  
  // Si l'utilisateur est sur une page publique et qu'il est connecté, rediriger vers le dashboard
  if (isPublicPath && token) {
    return NextResponse.redirect(new URL('/accueil', request.url));
  }
  
  // Si l'utilisateur n'est pas sur une page publique et qu'il n'est pas connecté, rediriger vers login
  if (!isPublicPath && !token) {
    return NextResponse.redirect(new URL('/users/login', request.url));
  }
  
  return NextResponse.next();
}

// Configurer pour que le middleware s'exécute sur toutes les routes sauf certains paths
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public directory)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};