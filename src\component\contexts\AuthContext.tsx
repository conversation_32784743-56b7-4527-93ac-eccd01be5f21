// contexts/AuthContext.tsx
"use client";
import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import authService from '@/component/auth/services/authService';

interface User {
  id_utilisateur: string;
  nom: string;
  prenom: string;
  email: string;
  role: string;
  statut?: boolean;
  telephone?: string;
  adresse?: string;
  username?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: Omit<User, 'id_utilisateur'> & { password: string }) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = () => {
      try {
        const currentUser = authService.getUser() as User;
        const token = authService.getAccessToken();
        
        if (currentUser && token) {
          setUser(currentUser);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error("Erreur lors de la vérification de l'authentification:", error);
        authService.logout();
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const { user: userData } = await authService.login(email, password);
      setUser(userData);
      setIsAuthenticated(true);
      router.push('/accueil');
    } catch (error) {
      console.error("Erreur de connexion:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: Omit<User, 'id_utilisateur'> & { password: string }) => {
    setLoading(true);
    try {
      const registrationData = {
        nom: userData.nom,
        prenom: userData.prenom,
        email: userData.email,
        role: userData.role,
        password: userData.password,
        telephone: userData.telephone || '',
        adresse: userData.adresse || '',
        statut: userData.statut || false,
        username: userData.username || ''
      };

      const { user: newUser } = await authService.register(registrationData);
      setUser(newUser);
      setIsAuthenticated(true);
      router.push('/accueil');
    } catch (error) {
      console.error("Erreur d'inscription:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = useCallback(() => {
    authService.logout();
    setUser(null);
    setIsAuthenticated(false);
    router.push('/users/login');
  }, [router]);

  const refreshTokenIfNeeded = useCallback(async () => {
    try {
      await authService.refreshToken();
    } catch (error) {
      console.error("Erreur lors du rafraîchissement du token:", error);
      logout();
    }
  }, [logout]);

  useEffect(() => {
    if (isAuthenticated) {
      const tokenCheckInterval = setInterval(() => {
        refreshTokenIfNeeded();
      }, 5 * 60 * 1000);

      return () => clearInterval(tokenCheckInterval);
    }
  }, [isAuthenticated, refreshTokenIfNeeded]);

  const contextValue: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    isAuthenticated
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};