import React from 'react';
import { Clock } from 'lucide-react';

interface ActivityItem {
  id: number;
  type: 'sale' | 'stock' | 'alert' | 'order';
  message: string;
  time: string;
}

const activities: ActivityItem[] = [
  { id: 1, type: 'sale', message: 'Nouvelle vente: Commande #1234 (350,00 €)', time: 'Il y a 5 minutes' },
  { id: 2, type: 'stock', message: 'Mise à jour du stock: +20 Écrans LCD 24"', time: 'Il y a 1 heure' },
  { id: 3, type: 'alert', message: 'Alerte: Stock faible pour Clavier sans fil (5 restants)', time: 'Il y a 2 heures' },
  { id: 4, type: 'order', message: 'Commande fournisseur #789 reçue', time: 'Il y a 3 heures' },
  { id: 5, type: 'sale', message: 'Nouvelle vente: Commande #1233 (125,50 €)', time: 'Il y a 5 heures' },
];

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'sale':
      return <span className="bg-green-100 text-green-600 p-1 rounded-full">💰</span>;
    case 'stock':
      return <span className="bg-blue-100 text-blue-600 p-1 rounded-full">📦</span>;
    case 'alert':
      return <span className="bg-red-100 text-red-600 p-1 rounded-full">⚠️</span>;
    case 'order':
      return <span className="bg-purple-100 text-purple-600 p-1 rounded-full">🚚</span>;
    default:
      return <span className="bg-gray-100 text-gray-600 p-1 rounded-full">📝</span>;
  }
};

const RecentActivity: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
        <div className="flex items-center">
          <Clock className="h-5 w-5 text-gray-400 mr-2" />
          <h3 className="text-lg font-medium leading-6 text-gray-900">Activité récente</h3>
        </div>
      </div>
      <ul className="divide-y divide-gray-200">
        {activities.map((activity) => (
          <li key={activity.id} className="px-4 py-4 sm:px-6 hover:bg-gray-50">
            <div className="flex items-center space-x-4">
              {getActivityIcon(activity.type)}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {activity.message}
                </p>
                <p className="text-sm text-gray-500 truncate">{activity.time}</p>
              </div>
            </div>
          </li>
        ))}
      </ul>
      <div className="px-4 py-3 bg-gray-50 text-right sm:px-6 rounded-b-lg">
        <button className="text-sm font-medium text-blue-600 hover:text-blue-500">
          Voir toutes les activités →
        </button>
      </div>
    </div>
  );
};

export default RecentActivity;