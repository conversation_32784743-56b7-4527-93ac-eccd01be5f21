
import { <PERSON><PERSON>, <PERSON>, User, Search, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Image from 'next/image';
interface HeaderProps {
  toggleSidebar: () => void;
}

interface User {
  id: number;
  nom: string;
  prenom: string;
  avatar_url?: string;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter()

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const token = localStorage.getItem('accessToken');
        if (!token) throw new Error('Non authentifié');
        const response = await fetch('http://localhost:8000/api/users/user/', {
          headers: {
            'Authorization': `Bear<PERSON> ${token}`,
          },
        });
        if (!response.ok) throw new Error('Erreur e recuperation');

        const data = await response.json();
        setUser(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur inconnue');
      } finally {
        setLoading(false);
      }






    };
    fetchUserProfile();
  }, [router]);

  const handlePush = () => {
    router.push('/profil')
  }

  if (loading) return (
    <div className="flex justify-center items-center h-screen">
      <Loader2 className="animate-spin h-12 w-12 text-blue-500" />
    </div>
  );

  if (error) return (
    <div className="p-4 text-red-500 bg-red-50 rounded-lg max-w-md mx-auto mt-10">
      {error}
    </div>
  );
  if (!user) return <p>Aucun utilisateur connecté</p>;

  return (
    <header className="bg-white shadow-sm z-10">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <button
              onClick={toggleSidebar}
              className="p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 lg:hidden"
            >
              <Menu size={22} />
            </button>
            <div className="hidden md:block ml-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={18} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Rechercher..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>
          </div>

          {/* CLOCHE NOTIFICATION  */}
          <div className="flex items-center">
            <button className="p-2 rounded-full text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 relative">
              <Bell size={20} />
              <span className="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-500"></span>
            </button>

            {/* IMGAE DE PROFILE  */}
            <div className="ml-4 relative flex items-center">
              <div className="flex items-center">
                <button onClick={handlePush} className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white hover:cursor-pointer">
                  {user?.avatar_url ? (
                    <Image
                      src={user.avatar_url}
                      alt={`${user.prenom} ${user.nom}`}
                      width={100}
                      height={100}
                      className="w-full h-full object-cover border-solid rounded"

                    />
                  )
                    : (
                      <User size={18} />
                    )}
                </button>
                <span className="ml-2 text-sm font-medium text-gray-700 hidden md:block">
                  {user.prenom} {user.nom}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile search bar */}
      <div className="px-4 py-2 md:hidden">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Rechercher..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>
    </header>
  );
};

export default Header;