// app/layout.tsx
"use client";
import { StrictMode } from 'react';
import { ReactNode } from "react";
import { usePathname } from "next/navigation";
import Layout from "@/component/Layout";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import { motion, AnimatePresence } from "framer-motion";
import Notification from "@/component/ui/Notification";
import { Providers } from "./provders"; // Correction de la faute de frappe ici
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({ children }: { children: ReactNode }) {
  const pathname = usePathname() || "";
  const isAuthPage = pathname.includes("/users/login") || pathname.includes("/users/register");
  
  return (
    <html lang="fr" className={`${geistSans.variable} ${geistMono.variable}`}>
      <body>
        <StrictMode>
          <Providers>
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Notification />
                {isAuthPage ? (
                  // Rendu direct sans Layout pour les pages d'authentification
                  <>{children}</>
                ) : (
                  // Avec Layout pour les autres pages
                  <Layout>{children}</Layout>
                )}
              </motion.div>
            </AnimatePresence>
          </Providers>
        </StrictMode>
      </body>
    </html>
  );
}