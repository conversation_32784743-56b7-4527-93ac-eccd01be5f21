"use client";
import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';

interface Product {
  id_produit: string;
  nom: string;
  reference: string;
}

interface Location {
  id: string;
  nom: string;
  entrepot: {
    id_entrepot: string;
    nom: string;
  };
}

interface AddStockModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  type: 'ENTREE' | 'SORTIE';
}

const AddStockModal: React.FC<AddStockModalProps> = ({ isOpen, onClose, onSuccess, type }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Form state
  const [productId, setProductId] = useState<string>('');
  const [locationId, setLocationId] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);
  const [notes, setNotes] = useState<string>('');

  // Fetch products and locations when modal opens
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch products
        const token = localStorage.getItem('accessToken');
        const productsResponse = await fetch('http://127.0.0.1:8000/api/mouvements/', {
          headers: {
            'Content-Type': 'application/json',
            // Ajoutez votre logique d'authentification ici si nécessaire
            'Authorization': `Bearer ${token}`,
          },
        });
        
        if (!productsResponse.ok) {
          throw new Error('Erreur lors de la récupération des produits');
        }
        const productsData = await productsResponse.json();
        setProducts(productsData);

        // Fetch locations
        const locationsResponse = await fetch('http://127.0.0.1:8000/api/entrepots/');
        if (!locationsResponse.ok) {
          throw new Error('Erreur lors de la récupération des emplacements');
        }
        const locationsData = await locationsResponse.json();
        setLocations(locationsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      } finally {
        setLoading(false);
      }
    };

    if (isOpen) {
      fetchData();
      // Reset form when opening
      setProductId('');
      setLocationId('');
      setQuantity(1);
      setNotes('');
      setError(null);
      setSuccess(false);
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('http://127.0.0.1:8000/api/mouvements/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          produit_id: productId,
          location_id: locationId,
          quantite: quantity,
          type_mouvement: type,
          utilisateur: "Utilisateur actuel", // À remplacer par l'utilisateur réel
          notes: notes
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erreur lors de l\'ajout du stock');
      }

      setSuccess(true);
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed z-10 inset-0 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                {type === 'ENTREE' ? 'Ajouter un stock' : 'Retirer un stock'}
              </h3>
              <button
                type="button"
                onClick={onClose}
                className="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            {success ? (
              <div className="mt-4 bg-green-50 p-4 rounded-md">
                <p className="text-green-800">
                  {type === 'ENTREE' ? 'Stock ajouté' : 'Stock retiré'} avec succès!
                </p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="mt-4">
                {error && (
                  <div className="mb-4 bg-red-50 p-4 rounded-md">
                    <p className="text-red-800">{error}</p>
                  </div>
                )}
                
                <div className="mb-4">
                  <label htmlFor="product" className="block text-sm font-medium text-gray-700">
                    Produit
                  </label>
                  <select
                    id="product"
                    value={productId}
                    onChange={(e) => setProductId(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    required
                    disabled={loading}
                  >
                    <option value="">Sélectionnez un produit</option>
                    {products.map((product) => (
                      <option key={product.id_produit} value={product.id_produit}>
                        {product.nom} ({product.reference})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="mb-4">
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700">
                    Emplacement
                  </label>
                  <select
                    id="location"
                    value={locationId}
                    onChange={(e) => setLocationId(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    required
                    disabled={loading}
                  >
                    <option value="">Sélectionnez un emplacement</option>
                    {locations.map((location) => (
                      <option key={location.id} value={location.id}>
                        {location.nom} ({location.entrepot.nom})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="mb-4">
                  <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                    Quantité
                  </label>
                  <input
                    type="number"
                    id="quantity"
                    min="1"
                    value={quantity}
                    onChange={(e) => setQuantity(Number(e.target.value))}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                    disabled={loading}
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                    Notes (optionnel)
                  </label>
                  <textarea
                    id="notes"
                    rows={3}
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    disabled={loading}
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    disabled={loading}
                  >
                    Annuler
                  </button>
                  <button
                    type="submit"
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    disabled={loading}
                  >
                    {loading ? (
                      'En cours...'
                    ) : type === 'ENTREE' ? (
                      'Ajouter le stock'
                    ) : (
                      'Retirer le stock'
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddStockModal;