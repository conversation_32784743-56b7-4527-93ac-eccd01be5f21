import React from 'react';
import { Bar<PERSON><PERSON> } from 'lucide-react';

interface TopProduct {
  id: number;
  name: string;
  sales: number;
  stock: number;
  percentage: number;
}

const topProducts: TopProduct[] = [
  { id: 1, name: 'Écran LCD 24"', sales: 45, stock: 23, percentage: 18 },
  { id: 2, name: 'Clavier sans fil', sales: 38, stock: 12, percentage: 15 },
  { id: 3, name: 'Souris optique', sales: 32, stock: 8, percentage: 13 },
  { id: 4, name: 'Disque SSD 500GB', sales: 28, stock: 15, percentage: 11 },
  { id: 5, name: 'Casque audio Bluetooth', sales: 25, stock: 20, percentage: 10 },
];

const TopProducts: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
        <div className="flex items-center">
          <BarChart className="h-5 w-5 text-blue-500 mr-2" />
          <h3 className="text-lg font-medium leading-6 text-gray-900">Produits les plus vendus</h3>
        </div>
      </div>
      
      <div className="px-4 py-3">
        {topProducts.map((product) => (
          <div key={product.id} className="mb-4 last:mb-0">
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm font-medium text-gray-700">{product.name}</span>
              <span className="text-sm text-gray-500">{product.sales} vendus</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                className="bg-blue-600 h-2.5 rounded-full" 
                style={{ width: `${product.percentage}%` }}
              ></div>
            </div>
            <div className="flex justify-between items-center mt-1">
              <span className="text-xs text-gray-500">{product.percentage}% des ventes</span>
              <span className="text-xs text-gray-500">Stock: {product.stock}</span>
            </div>
          </div>
        ))}
      </div>
      
      <div className="px-4 py-3 bg-gray-50 text-right sm:px-6 rounded-b-lg">
        <button className="text-sm font-medium text-blue-600 hover:text-blue-500">
          Voir tous les produits →
        </button>
      </div>
    </div>
  );
};

export default TopProducts;