// components/auth/RegisterForm.tsx
"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Loader2, AlertCircle } from "lucide-react";

interface RegisterFormData {
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  adresse: string;
  entreprise_id: string;
  password: string;
  confirmPassword?: string;
}

interface EntrepriseForm {
  id: string;
  nom: string;
  adresse: string;
  nif: string;
  date_creation: string;
  statut: boolean;
  administrateur: {
    id_utilisateur: string;
    username: string;
    nom: string;
    prenom: string;
    email: string;
  };
}

interface RegisterResponse {
  user: {
    id: string;
    name: string;
    email: string;
    
  };
  refresh: string;
  access: string;
  message?: string;
  errors?: {
    [key: string]: string;
  };
}

export default function Register() {
  const router = useRouter();
  const [formData, setFormData] = useState<RegisterFormData>({
    nom: "",
    prenom: "",
    email: "",
    telephone: "",
    adresse: "",
    entreprise_id: "",
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState<{ [key: string]: string } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    // Validation des champs requis
    if (!formData.nom.trim()) {
      newErrors.nom = "Le nom est requis";
    }
    if (!formData.prenom.trim()) {
      newErrors.prenom = "Le prénom est requis";
    }
    if (!formData.email.trim()) {
      newErrors.email = "L'email est requis";
    }
    if (!formData.telephone.trim()) {
      newErrors.telephone = "Le téléphone est requis";
    }
    if (!formData.adresse.trim()) {
      newErrors.adresse = "L'adresse est requise";
    }
    if (!formData.entreprise_id) {
      newErrors.entreprise_id = "Veuillez sélectionner une entreprise";
    }
    if (!formData.password) {
      newErrors.password = "Le mot de passe est requis";
    }
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Les mots de passe ne correspondent pas";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors(null);

    // Enlever confirmPassword pour l'envoi à l'API
    const dataToSend = { ...formData };
    delete dataToSend.confirmPassword;



    try {
      const response = await fetch(`http://localhost:8000/api/auth/register/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dataToSend),
      });

      const data: RegisterResponse = await response.json();

      if (!response.ok) {
        if (data.errors) {
          setErrors(data.errors);
        } else {
          throw new Error(data.message || "Erreur d'inscription");
        }
        return;
      }

      // Sauvegarde des tokens et des infos utilisateur
      localStorage.setItem("accessToken", data.access);
      localStorage.setItem("refreshToken", data.refresh);
      localStorage.setItem("user", JSON.stringify(data.user));

      // Redirection vers le dashboard
      router.push("/accueil");
    } catch (err) {
      setErrors({
        general: err instanceof Error ? err.message : "Une erreur est survenue",
      });
    } finally {
      setIsLoading(false);
    }
  };


  const [Entreprise, setEntreprise] = useState<EntrepriseForm[]>([]);
  const [loadingEntreprises, setLoadingEntreprises] = useState(true);
  const [errorEntreprises, setErrorEntreprises] = useState<string | null>(null);

  useEffect(() => {
    const fetchEntreprise = async () => {
      try {
        setLoadingEntreprises(true);
        const reponse = await fetch('http://localhost:8000/api/entreprises/');
        if (!reponse.ok) {
          throw new Error('Erreur lors du chargement des Entreprises');
        }
        const data = await reponse.json();
        setEntreprise(data);
        setLoadingEntreprises(false);
      } catch (err) {
        setErrorEntreprises('Erreur lors du chargement ');
        setLoadingEntreprises(false);
        console.error('Erreur de chargement:', err);
      }
    };

    fetchEntreprise();

  }, []);



  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="max-w-lg w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Créer un compte
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="nom" className="block text-sm font-medium text-gray-700">
                  Nom
                </label>
                <input
                  id="nom"
                  name="nom"
                  type="text"
                  required
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Nom"
                  value={formData.nom}
                  onChange={handleChange}
                />
                {errors?.nom && (
                  <p className="mt-1 text-sm text-red-600">{errors.nom}</p>
                )}
              </div>
              <div>
                <label htmlFor="prenom" className="block text-sm font-medium text-gray-700">
                  Prénom
                </label>
                <input
                  id="prenom"
                  name="prenom"
                  type="text"
                  required
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Prénom"
                  value={formData.prenom}
                  onChange={handleChange}
                />
                {errors?.prenom && (
                  <p className="mt-1 text-sm text-red-600">{errors.prenom}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Email"
                value={formData.email}
                onChange={handleChange}
              />
              {errors?.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            <div>
              <label htmlFor="telephone" className="block text-sm font-medium text-gray-700">
                Téléphone
              </label>
              <input
                id="telephone"
                name="telephone"
                type="tel"
                required
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Téléphone"
                value={formData.telephone}
                onChange={handleChange}
              />
              {errors?.telephone && (
                <p className="mt-1 text-sm text-red-600">{errors.telephone}</p>
              )}
            </div>

            <div>
              <label htmlFor="adresse" className="block text-sm font-medium text-gray-700">
                Adresse
              </label>
              <textarea
                id="adresse"
                name="adresse"
                required
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Adresse"
                value={formData.adresse}
                onChange={handleChange}
                rows={3}
              />
              {errors?.adresse && (
                <p className="mt-1 text-sm text-red-600">{errors.adresse}</p>
              )}
            </div>

            <div>
              <label htmlFor="entreprise_id" className="block text-sm font-medium text-gray-700">
                Entreprise
              </label>
              <select
                id="entreprise_id"
                name="entreprise_id"
                required
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                value={formData.entreprise_id}
                onChange={handleChange}
              >
                <option value="">
                  {loadingEntreprises ? "Chargement des entreprises..." : errorEntreprises ? "Erreur de chargement" : "-- Sélectionner une entreprise --"}
                </option>
                {!loadingEntreprises && !errorEntreprises && Entreprise.map((e) => (
                  <option key={e.id} value={e.id}>
                    {e.nom}
                  </option>
                ))}
              </select>
              {errors?.entreprise_id && (
                <p className="mt-1 text-sm text-red-600">{errors.entreprise_id}</p>
              )}
            </div>

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Mot de passe
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Mot de passe"
                  value={formData.password}
                  onChange={handleChange}
                />
                {errors?.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password}</p>
                )}
              </div>
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                  Confirmer le mot de passe
                </label>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  required
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Confirmer le mot de passe"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                />
                {errors?.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
                )}
              </div>
            </div>
          </div>

          {errors?.general && (
            <div className="flex items-center p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50">
              <AlertCircle className="w-5 h-5 mr-2" />
              <span>{errors.general}</span>
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {isLoading ? (
                <Loader2 className="w-5 h-5 animate-spin mr-2" />
              ) : (
                "S'inscrire"
              )}
            </button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Vous avez déjà un compte ?{" "}
              <Link href="/users/login" className="font-medium text-indigo-600 hover:text-indigo-500">
                Se connecter
              </Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}