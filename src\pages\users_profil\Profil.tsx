'use client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2, Edit, LogOut, User } from 'lucide-react';
import Image from 'next/image';

interface User {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  role?: string;
  createdAt?: string;
  avatar_url?: string;
}

export default function ProfilPage() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const token = localStorage.getItem('accessToken');
        if (!token) throw new Error('Non authentifié');

        const response = await fetch('http://localhost:8000/api/users/user/', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) throw new Error('Erreur de récupération');
        
        const data = await response.json();
        setUser(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur inconnue');
        router.push('/users/login');
      } finally {
        setLoading(false);
      }
      
    };

    fetchUserProfile();
  }, [router]);

  useEffect(()=> {
    console.log(user)
  })

  const handleLogout = () => {
    localStorage.removeItem('accessToken');
    router.push('/users/login');
  };

  if (loading) return (
    <div className="flex justify-center items-center h-screen">
      <Loader2 className="animate-spin h-12 w-12 text-blue-500" />
    </div>
  );

  if (error) return (
    <div className="p-4 text-red-500 bg-red-50 rounded-lg max-w-md mx-auto mt-10">
      {error}
    </div>
  );

 

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-xl shadow-md overflow-hidden md:max-w-2xl">
        <div className="p-8">
          {/* Section Avatar + Infos */}
          <div className="flex items-start space-x-6">
            {/* Avatar Container */}
            <div className="relative">
              <div className="w-24 h-24 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden border-2 border-white shadow-md">
                {user?.avatar_url ? (
                 <Image
                 src={user.avatar_url}
                 alt={`${user.prenom} ${user.nom}`}
                 width={96}
                 height={96}
                 className="w-full h-full object-cover"
                 
               />
                ) : (
                  <User className="text-gray-500 w-12 h-12" />
                )}
              </div>
              <button
                onClick={() => router.push('/profil/edit')}
                className="absolute -top-2 -right-2 bg-blue-600 p-1 rounded-full text-white hover:bg-blue-700"
              >
                <Edit size={16} />
              </button>
            </div>

            {/* User Info */}
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-800">
                {user?.prenom} {user?.nom}
              </h1>
              <p className="mt-1 text-gray-500">{user?.email}</p>
              <p className="mt-1 text-sm text-gray-500">
                <span className="font-medium">Rôle:</span> {user?.role || 'Non spécifié'}
              </p>
            </div>
          </div>

          {/* Additional Info */}
          <div className="mt-6 space-y-4">
            <div className="border-t border-gray-200 pt-4">
              <h2 className="text-sm font-medium text-gray-500">Détails</h2>
              <div className="mt-2 space-y-2">
                <p><span className="font-medium">Membre depuis:</span> {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Logout Button */}
          <div className="mt-8">
            <button
              onClick={handleLogout}
              className="w-full flex justify-center items-center gap-2 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
            >
              <LogOut size={16} />
              Déconnexion
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}