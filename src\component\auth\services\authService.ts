// services/authService.ts
interface User {
    id_utilisateur: string;
    nom: string;
    prenom: string;
    email: string;
    role: string;
    // autres champs utilisateur
  }
  
  interface AuthTokens {
    access: string;
    refresh: string;
  }
  
  class AuthService {
    private static instance: AuthService;
    private apiUrl: string;
  
    private constructor() {
      this.apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    }
  
    public static getInstance(): AuthService {
      if (!AuthService.instance) {
        AuthService.instance = new AuthService();
      }
      return AuthService.instance;
    }
  
    async login(email: string, password: string): Promise<{ user: User; tokens: AuthTokens }> {
      const response = await fetch(`${this.apiUrl}/api/auth/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });
  
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || 'Échec de connexion');
      }
  
      const data = await response.json();
      
      // Sauvegarder les données dans localStorage
      localStorage.setItem('accessToken', data.access);
      localStorage.setItem('refreshToken', data.refresh);
      localStorage.setItem('user', JSON.stringify(data.user));
  
      return {
        user: data.user,
        tokens: {
          access: data.access,
          refresh: data.refresh,
        },
      };
    }
  
    async register(userData: {
      nom: string;
      prenom: string;
      email: string;
      telephone: string;
      adresse: string;
      role: string;
      password: string;
    }): Promise<{ user: User; tokens: AuthTokens }> {
      const response = await fetch(`${this.apiUrl}/api/auth/register/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });
  
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.errors || errorData.message || 'Échec d\'inscription');
      }
  
      const data = await response.json();
      
      // Sauvegarder les données dans localStorage
      localStorage.setItem('accessToken', data.access);
      localStorage.setItem('refreshToken', data.refresh);
      localStorage.setItem('user', JSON.stringify(data.user));
  
      return {
        user: data.user,
        tokens: {
          access: data.access,
          refresh: data.refresh,
        },
      };
    }
  
    async refreshToken(): Promise<string> {
      const refreshToken = localStorage.getItem('refreshToken');
      
      if (!refreshToken) {
        throw new Error('Aucun refresh token disponible');
      }
  
      const response = await fetch(`${this.apiUrl}/api/auth/token/refresh/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh: refreshToken }),
      });
  
      if (!response.ok) {
        // Si le refresh token est invalide, déconnecter l'utilisateur
        this.logout();
        throw new Error('Session expirée, veuillez vous reconnecter');
      }
  
      const data = await response.json();
      localStorage.setItem('accessToken', data.access);
      
      return data.access;
    }
  
    getUser(): User | null {
      const userStr = typeof window !== 'undefined' ? localStorage.getItem('user') : null;
      if (!userStr) return null;
      
      try {
        return JSON.parse(userStr);
      } catch {
        return null;
      }
    }
  
    isAuthenticated(): boolean {
      const token = typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;
      return !!token;
    }
  
    logout(): void {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }
  
    getAccessToken(): string | null {
      return typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;
    }
  }
  
  export default AuthService.getInstance();